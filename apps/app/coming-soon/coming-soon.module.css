.container {
    background-color: #0b0f19;
    color: white;
    height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
  }
  
  .glowBox {
    text-align: center;
    max-width: 700px;
    padding: 3rem;
    border: 2px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.02);
    border-radius: 1rem;
    box-shadow: 0 0 60px rgba(255, 255, 255, 0.05);
  }
  
  .title {
    font-size: 3rem;
    font-weight: bold;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  }
  
  .subtitle {
    font-size: 1.25rem;
    margin-top: 1rem;
    color: #d1d5db;
  }
  
  .funky {
    color: #4f46e5;
    font-weight: 700;
  }
  
  .buttonGroup {
    margin-top: 2rem;
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
  }
  
  .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    background-color: white;
    color: black;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
  }
  
  .btn:hover {
    background-color: #f3f4f6;
  }
  
  .btnSecondary {
    padding: 0.75rem 1.5rem;
    border: 1px solid white;
    color: white;
    background: transparent;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
  }
  
  .btnSecondary:hover {
    background-color: white;
    color: black;
  }
  