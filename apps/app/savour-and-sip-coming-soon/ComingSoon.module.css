.comingSoonContainer {
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #0d0d0d, #1a1a1a);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: white;
    text-align: center;
    overflow: hidden;
  }
  
  .content {
    max-width: 600px;
  }
  
  .heading {
    font-size: 56px;
    font-weight: 700;
    color: #cba135; /* Golden luxury */
    margin-bottom: 20px;
  }
  
  .subheading {
    font-size: 24px;
    margin-bottom: 10px;
    color: #ddd;
  }
  
  .description {
    font-size: 18px;
    color: #aaa;
    margin-top: 10px;
    line-height: 1.6;
  }
  
  /* Responsive for Mobile */
  @media (max-width: 768px) {
    .heading {
      font-size: 38px;
    }
  
    .subheading {
      font-size: 20px;
    }
  
    .description {
      font-size: 16px;
    }
  }
  