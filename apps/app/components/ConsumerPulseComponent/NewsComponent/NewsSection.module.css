.newsSection {
    padding: 4rem 2rem;
    background: #fff;
    border-top: 1px solid #e0e0e0;
  }
  
  .sectionTitle {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 2rem;
    font-family: 'Helvetica Neue', sans-serif;
    color: #111;
  }
  
  .newsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }
  
  .newsCard {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    border-bottom: 1px solid #ddd;
    padding-bottom: 1.5rem;
  }
  
  .image {
    width: 100%;
    height: auto;
    border-radius: 4px;
    object-fit: cover;
  }
  
  .title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #000;
    font-family: 'Helvetica Neue', sans-serif;
  }
  
  .desc {
    font-size: 0.95rem;
    color: #444;
    line-height: 1.5;
  }
  