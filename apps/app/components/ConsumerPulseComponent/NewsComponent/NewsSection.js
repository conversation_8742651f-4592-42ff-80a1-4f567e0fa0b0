'use client';
import { useState, useEffect } from 'react';
import styles from './NewsSection.module.css';

export default function NewsSection() {
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchArticles();

    // Set up auto-refresh every 5 minutes
    const interval = setInterval(fetchArticles, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  const fetchArticles = async () => {
    try {
      const response = await fetch('/api/consumer-pulse/articles?status=PUBLISHED&limit=6');
      const data = await response.json();

      if (data.success) {
        setArticles(data.articles);
      }
    } catch (error) {
      console.error('Error fetching articles:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fallback data if no articles found
  const fallbackData = [
    {
      id: 1,
      title: "<PERSON> Says His Funds Hold 95% in Cash on Trade War Risks",
      description:
        "Veteran investor <PERSON> warns of market volatility as geopolitical tensions rise.",
      image: "/TradeWar.jpg", // Replace with actual image paths
    },
    {
      id: 2,
      title: "China Manufacturing Slumps on US Levies, Spurring Stimulus Calls",
      description:
        "China’s industrial output slowed sharply, prompting calls for central policy support.",
      image: "/USAChina.jpg",
    },
    {
        id: 3,
        title: "Mark Mobius Says His Funds Hold 95% in Cash on Trade War Risks",
        description:
          "Veteran investor Mark Mobius warns of market volatility as geopolitical tensions rise.",
        image: "/TradeWar.jpg", // Replace with actual image paths
      },
      {
        id: 4,
        title: "China Manufacturing Slumps on US Levies, Spurring Stimulus Calls",
        description:
          "China’s industrial output slowed sharply, prompting calls for central policy support.",
        image: "/USAChina.jpg",
      },
  ];

  const displayData = articles.length > 0 ? articles : fallbackData;

  if (loading) {
    return (
      <section className={styles.newsSection}>
        <h2 className={styles.sectionTitle}>Top Stories</h2>
        <div className={styles.loadingGrid}>
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className={styles.loadingCard}>
              <div className={styles.loadingImage}></div>
              <div className={styles.loadingText}></div>
              <div className={styles.loadingDesc}></div>
            </div>
          ))}
        </div>
      </section>
    );
  }

  return (
    <section className={styles.newsSection}>
      <div className={styles.sectionHeader}>
        <h2 className={styles.sectionTitle}>Top Stories</h2>
        <button
          onClick={fetchArticles}
          className={styles.refreshButton}
          disabled={loading}
        >
          {loading ? '🔄' : '↻'} Refresh
        </button>
      </div>

      <div className={styles.newsGrid}>
        {displayData.map((item, index) => (
          <div key={item.id || index} className={styles.newsCard}>
            <img
              src={item.imageUrl || item.image || "/Trump.jpg"}
              alt={item.title}
              className={styles.image}
            />
            <h3 className={styles.title}>{item.title}</h3>
            <p className={styles.desc}>
              {item.summary || item.description || item.content?.substring(0, 120) + '...'}
            </p>
            {item.category && (
              <div className={styles.metadata}>
                <span className={styles.category}>{item.category}</span>
                {item.sentiment && (
                  <span className={styles.sentiment} data-sentiment={item.sentiment}>
                    {item.sentiment}
                  </span>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </section>
  );
}
