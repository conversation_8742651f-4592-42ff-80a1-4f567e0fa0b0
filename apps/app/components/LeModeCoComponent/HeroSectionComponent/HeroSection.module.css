.hero {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.bgVideo {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
  opacity: 0.7;
}

.heroContent {
  position: relative;
  z-index: 1;
  text-align: center;
  color: white;
  padding: 2rem;
  max-width: 800px;
}

.heroContent h1 {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 1rem;
  letter-spacing: 2px;
}

.heroContent p {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  font-weight: 300;
}

.heroContent button {
  padding: 14px 32px;
  background-color: transparent;
  border: 2px solid white;
  font-size: 1.2rem;
  border-radius: 999px;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.heroContent button:hover {
  background-color: white;
  color: black;
}

/* Scroll Fade Effect */
.heroContent.fadeOut {
  opacity: 0.5;
  transform: scale(0.95);
}

/* 📱 Mobile Responsive */
@media (max-width: 768px) {
  .heroContent h1 {
    font-size: 2.5rem;
  }

  .heroContent p {
    font-size: 1.1rem;
  }
}
