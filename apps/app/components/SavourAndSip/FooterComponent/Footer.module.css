.footer {
    background-color: white;
    padding: 60px 20px;
    border-top: 1px solid #e5e5e5;
    font-family: 'Georgia', serif;
  }
  
  .footerTop {
    text-align: center;
    margin-bottom: 40px;
  }
  
  .brand {
    font-size: 28px;
    color: #cba135;
    letter-spacing: 2px;
  }
  
  .footerContent {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    max-width: 1200px;
    margin: 0 auto 40px auto;
  }
  
  .column {
    flex: 1;
    min-width: 220px;
    padding: 10px;
  }
  
  .column h4 {
    font-size: 18px;
    color: #222;
    margin-bottom: 20px;
  }
  
  .column p {
    font-size: 16px;
    color: #555;
    margin-bottom: 12px;
  }
  
  .column a {
    color: #555;
    text-decoration: none;
    position: relative;
  }
  
  .column a::after {
    content: '';
    position: absolute;
    width: 0%;
    height: 1px;
    bottom: -2px;
    left: 0;
    background-color: #cba135;
    transition: width 0.3s ease;
  }
  
  .column a:hover::after {
    width: 100%;
  }
  
  .footerBottom {
    text-align: center;
    font-size: 14px;
    color: #777;
  }
  