.featureSection {
  background-color: white;
  padding: 100px 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 80px;
}

.featureCard {
  background-color: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.featureCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
}

.smallTitle {
  color: #cba135;
  text-transform: uppercase;
  font-size: 18px;
  letter-spacing: 1.5px;
  margin-bottom: 15px;
}

.description {
  font-size: 18px;
  color: #555;
  line-height: 1.8;
}

@media (max-width: 768px) {
  .container {
    gap: 50px;
  }

  .featureCard {
    padding: 30px 20px;
  }

  .description {
    font-size: 16px;
  }
}
