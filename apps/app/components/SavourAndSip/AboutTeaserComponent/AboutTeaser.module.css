.aboutSection {
  padding: 6rem 2rem;
  background: #ffffff;
  position: relative;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

.contentWrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.textContent {
  padding-right: 2rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 2rem;
  letter-spacing: -0.02em;
}

.description {
  font-size: 1.25rem;
  color: #4a4a4a;
  line-height: 1.7;
  margin-bottom: 2.5rem;
}

.ctaButton {
  background: linear-gradient(135deg, #d4af37, #f4e4a6);
  color: #1a1a1a;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.ctaButton:hover {
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
  transform: translateY(-2px);
}

.imageContent {
  position: relative;
}

.imageWrapper {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.mainImage {
  width: 100%;
  height: 400px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.imageWrapper:hover .mainImage {
  transform: scale(1.05);
}

.imageOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 2rem;
  color: white;
}

.overlayContent {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.overlayText {
  background: rgba(212, 175, 55, 0.9);
  color: #1a1a1a;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contentWrapper {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }
  
  .textContent {
    padding-right: 0;
  }
}

@media (max-width: 768px) {
  .aboutSection {
    padding: 4rem 1rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .description {
    font-size: 1.1rem;
  }
  
  .mainImage {
    height: 300px;
  }
  
  .imageOverlay {
    padding: 1.5rem;
  }
  
  .overlayContent {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.75rem;
  }
  
  .description {
    font-size: 1rem;
  }
  
  .ctaButton {
    padding: 0.875rem 1.5rem;
    font-size: 0.95rem;
  }
  
  .mainImage {
    height: 250px;
  }
}
