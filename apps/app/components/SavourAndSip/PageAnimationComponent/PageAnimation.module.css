.loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: black;
    color: #cba135; /* Golden */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeOut 1s ease 2.4s forwards;
  }
  
  .loader h1 {
    font-family: 'Georgia', serif;
    font-size: 36px;
    letter-spacing: 2px;
    opacity: 1;
    animation: fadeIn 1s ease forwards;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  @keyframes fadeOut {
    to {
      opacity: 0;
      visibility: hidden;
    }
  }
  