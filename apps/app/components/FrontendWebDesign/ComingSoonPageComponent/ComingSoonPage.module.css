.wrapper {
  position: relative;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background-color: black;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bgVideo {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  object-fit: cover;
  z-index: 0;
  opacity: 0.5;
}

.overlay {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  box-sizing: border-box;
}

.title {
  font-size: 4rem;
  font-weight: bold;
  letter-spacing: 0.2rem;
  animation: glow 2s ease-in-out infinite alternate;
  max-width: 90%;
  word-wrap: break-word;
}

@keyframes glow {
  from {
    text-shadow: 0 0 10px #4f46e5, 0 0 20px #4f46e5;
  }
  to {
    text-shadow: 0 0 20px #6366f1, 0 0 40px #6366f1;
  }
}

.description {
  font-size: 1.25rem;
  margin-top: 1rem;
  color: #d1d5db;
  max-width: 85%;
  word-wrap: break-word;
}

.loader {
  margin-top: 2rem;
  width: 40px;
  height: 40px;
  border: 4px solid white;
  border-top: 4px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ✅ Mobile Optimizations */
@media (max-width: 768px) {
  .title {
    font-size: 2.2rem;
    letter-spacing: 0.05rem;
  }

  .description {
    font-size: 1rem;
    margin-top: 1rem;
    max-width: 90%;
  }

  .loader {
    width: 32px;
    height: 32px;
    border-width: 3px;
  }
}
