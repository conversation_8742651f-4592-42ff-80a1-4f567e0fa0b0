.whyChooseSection {
  padding: 6rem 2rem;
  background: white;
  position: relative;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 4rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.subtitle {
  font-size: 1.25rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 5rem;
}

.featureCard {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 2.5rem 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.featureCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.featureCard:hover::before {
  transform: scaleX(1);
}

.featureCard:hover {
  border-color: #667eea;
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.1);
  transform: translateY(-5px);
}

.featureIcon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  display: block;
}

.featureTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.featureDescription {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

.ctaSection {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 24px;
  padding: 4rem 3rem;
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;
}

.ctaSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.ctaTitle {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  position: relative;
  z-index: 1;
}

.ctaDescription {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
  position: relative;
  z-index: 1;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
}

.primaryButton {
  background: white;
  color: #667eea;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.primaryButton:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.secondaryButton {
  background: transparent;
  color: white;
  border: 2px solid white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.secondaryButton:hover {
  background: white;
  color: #667eea;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .whyChooseSection {
    padding: 4rem 1rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1.1rem;
  }
  
  .featuresGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 3rem;
  }
  
  .featureCard {
    padding: 2rem 1.5rem;
  }
  
  .ctaSection {
    padding: 3rem 2rem;
  }
  
  .ctaTitle {
    font-size: 1.75rem;
  }
  
  .ctaDescription {
    font-size: 1.1rem;
  }
  
  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .featureCard {
    padding: 1.5rem;
  }
  
  .featureIcon {
    font-size: 2.5rem;
  }
  
  .ctaSection {
    padding: 2rem 1.5rem;
  }
  
  .ctaTitle {
    font-size: 1.5rem;
  }
  
  .ctaDescription {
    font-size: 1rem;
  }
}
