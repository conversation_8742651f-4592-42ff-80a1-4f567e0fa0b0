.section {
  background-color: #f9fafb;
  padding: 5rem 2rem;
  color: #111827;
  text-align: center;
  overflow-x: hidden;
  box-sizing: border-box;
}

.heading {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 3rem;
}

.cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;              /* ✅ Prevent content touching screen edge */
  box-sizing: border-box;       /* ✅ Consistent padding */
}

.card {
  background-color: #ffffff;
  padding: 2rem;
  border-radius: 1rem;
  width: 100%;                  /* ✅ Full width by default */
  max-width: 320px;             /* ✅ Cap card size */
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.07);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #f3f4f6;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #fbbf24; /* Interac Yellow */
}

.card h3 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.75rem;
}

.card p {
  font-size: 0.95rem;
  line-height: 1.5;
  color: #4b5563;
}

/* 📱 Mobile Responsive */
@media (max-width: 768px) {
  .cards {
    flex-direction: column;
    align-items: center;
  }

  .card {
    width: 75%; /* Safe padding on mobile */
  }

  .heading {
    font-size: 1.6rem;
  }
}
