.header {
  background-color: #ffffff;
  padding: 1rem 2rem;
  position: sticky;
  top: 0;
  width: 100%;
  z-index: 999;
  border-bottom: 1px solid #f3f4f6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logoWithIcon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: #1f2937;
}

.logoText {
  font-size: 1.25rem;
  font-weight: 700;
}

.navbar {
  display: flex;
  align-items: center;
}

.navLinks {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.navItem {
  color: #4b5563;
  text-decoration: none;
  font-weight: 500;
}

.navItem:hover {
  color: #111827;
}

.navItemWrapper {
  position: relative;
}

.dropdown {
  position: absolute;
  top: 2.5rem;
  left: 0;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 0.5rem 0;
  min-width: 200px;
  z-index: 1000;
}

.dropdownItem {
  display: block;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  text-decoration: none;
  color: #4b5563;
}

.dropdownItem:hover {
  background-color: #facc15;
  color: #1f2937;
}

/* .ctaButton {
  background-color: #facc15;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 600;
  color: #1f2937;
} */

.signInPrompt {
  font-size: 0.85rem;
  color: #6b7280;
}
.ctaButton {
  background-color: #facc15;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 600;
  color: #1f2937;
  text-decoration: none; /* 🔥 remove underline */
}

.signInLink {
  margin-left: 0.25rem;
  color: #1f2937;
  font-weight: 500;
  text-decoration: underline;
}

/* 🔥 Mobile Styles */
.mobileMenuButton {
  display: none;
  background: none;
  border: none;
  font-size: 1.75rem;
  color: #1f2937;
  margin-left: auto;
  padding-right: 3rem;
}

.mobileMenu {
  position: fixed;
  top: 0;
  right: 0;
  background: white;
  width: 80%;
  height: 100%;
  z-index: 2000;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  animation: slideIn 0.3s ease-in-out;
}

.menuSlide {
  animation: slideOut 0.3s ease-in-out reverse;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0%);
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(100%);
  }
}

.closeBtn {
  align-self: flex-end;
  font-size: 2rem;
  background: none;
  border: none;
  cursor: pointer;
}

.mobileDropdownWrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobileDropdownToggle {
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  color: #1f2937;
  cursor: pointer;
  text-align: left;
  padding: 0;
}

.mobileDropdown {
  padding-left: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

@media (max-width: 768px) {
  .mobileMenuButton {
    display: block;
  }

  .navLinks {
    display: none;
  }
}
