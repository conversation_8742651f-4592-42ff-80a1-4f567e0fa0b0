.demoSection {
  padding: 4rem 2rem;
  background-color: #f9fafb;
  color: #1f2937;
  text-align: center;
  max-width: 100vw;
  box-sizing: border-box;
}

.heading {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2.5rem;
  color: #111827;
}

.wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2.5rem;
  align-items: flex-start;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.left {
  flex: 1;
  min-width: 280px;
  text-align: left;
  box-sizing: border-box;
}

.left label {
  font-weight: 600;
  display: block;
  margin-bottom: 0.5rem;
  color: #374151;
}

.dropdown {
  width: 60%;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  background-color: #ffffff;
  color: #111827;
  border: 1px solid #d1d5db;
  margin-bottom: 2rem;
  font-size: 1rem;
  box-sizing: border-box;
}

.features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features li {
  margin-bottom: 0.75rem;
  font-size: 1rem;
  color: #4b5563;
}

.right {
  flex: 1;
  min-width: 300px;
  text-align: center;
  box-sizing: border-box;
}

.demoVideo {
  width: 100%;
  border-radius: 0.75rem;
  margin-bottom: 1rem;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}

.demoButton {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background-color: #facc15; /* Yellow highlight */
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  color: #1f2937;
  text-decoration: none;
  transition: all 0.3s ease;
}

.demoButton:hover {
  background-color: #eab308;
  color: black;
}

/* 📱 Responsive */
@media (max-width: 768px) {
  .wrapper {
    flex-direction: column;
    align-items: stretch;
  }

  .left,
  .right {
    width: 100%;
  }

  .heading {
    font-size: 1.6rem;
  }
}
