.login-container {
  min-height: 100vh;
  background-color: #ffffff;
  color: #1f2937;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  box-sizing: border-box;
}

.login-heading {
  font-size: 2rem;
  margin-bottom: 2rem;
  font-weight: 700;
  text-align: center;
  color: #1f2937;
}

.login-form {
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-input {
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  background-color: #f9fafb;
  color: #111827;
  border: 1px solid #d1d5db;
  font-size: 1rem;
}

.form-input:focus {
  border-color: #facc15;
  outline: none;
  background-color: #fffbea;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: #4b5563;
}

.forgot-link {
  background: none;
  border: none;
  color: #f59e0b;
  cursor: pointer;
  text-decoration: underline;
  font-weight: 500;
}

.login-button {
  background-color: #facc15;
  border: none;
  padding: 0.85rem;
  border-radius: 0.5rem;
  font-weight: 600;
  color: #111827;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.login-button:hover {
  background-color: #fbbf24;
}

/* 📱 Responsive */
@media (max-width: 768px) {
  .login-form {
    width: 100%;
  }

  .login-heading {
    font-size: 1.7rem;
  }
}
