.eventsPage {
  min-height: 100vh;
  background: #ffffff;
}

/* Navigation styles removed - using SharedHeader component */

/* Hero Section */
.heroSection {
  position: relative;
  height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.heroVideo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.heroVideo video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.heroOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.7), rgba(45, 45, 45, 0.5));
  z-index: 2;
}

.heroContent {
  position: relative;
  z-index: 3;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
  margin-bottom: 1rem;
}

.highlight {
  color: #d4af37;
}

.heroSubtitle {
  font-size: 1.25rem;
  color: #cccccc;
  line-height: 1.6;
}

/* Container */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Events Section */
.eventsSection {
  padding: 6rem 0;
  background: #f8f9fa;
}

.eventsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.eventCard {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  cursor: pointer;
}

.eventCard:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
  border-color: #d4af37;
}

.eventMedia {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.eventVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.eventCard:hover .eventVideo {
  transform: scale(1.1);
}

.eventOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 2rem;
  color: white;
}

.overlayContent {
  text-align: left;
}

.eventTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #d4af37;
}

.eventDescription {
  font-size: 1rem;
  color: #cccccc;
  line-height: 1.5;
  margin: 0;
}

.eventContent {
  padding: 2rem;
}

.eventFeatures {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.featureTag {
  background: #f8f9fa;
  color: #4a4a4a;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid #e9ecef;
}

.eventButton {
  background: linear-gradient(135deg, #d4af37, #f4e4a6);
  color: #1a1a1a;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.eventButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

/* Process Section */
.processSection {
  padding: 6rem 0;
  background: white;
}

.processHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.processTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.processSubtitle {
  font-size: 1.25rem;
  color: #4a4a4a;
  line-height: 1.6;
}

.processSteps {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.processStep {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 16px;
  border-left: 4px solid #d4af37;
}

.stepNumber {
  font-size: 2rem;
  font-weight: 700;
  color: #d4af37;
  min-width: 60px;
}

.stepContent {
  flex: 1;
}

.stepTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.stepDescription {
  font-size: 1rem;
  color: #4a4a4a;
  line-height: 1.6;
  margin: 0;
}

/* CTA Section */
.ctaSection {
  padding: 6rem 0;
  background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
  color: white;
  text-align: center;
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  color: #d4af37;
}

.ctaDescription {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  line-height: 1.6;
  color: #cccccc;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton {
  background: linear-gradient(135deg, #d4af37, #f4e4a6);
  color: #1a1a1a;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.primaryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.secondaryButton {
  background: transparent;
  color: white;
  border: 2px solid #d4af37;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondaryButton:hover {
  background: #d4af37;
  color: #1a1a1a;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .eventsGrid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .heroSubtitle {
    font-size: 1.1rem;
  }
  
  .eventsGrid {
    grid-template-columns: 1fr;
  }
  
  .processStep {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .ctaTitle {
    font-size: 2rem;
  }
  
  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }
  
  .eventContent {
    padding: 1.5rem;
  }
  
  .container {
    padding: 0 1rem;
  }
}
