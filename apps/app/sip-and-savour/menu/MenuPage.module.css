.menuPage {
  min-height: 100vh;
  background: #ffffff;
}

/* Navigation styles removed - using SharedHeader component */

/* Hero Section */
.heroSection {
  position: relative;
  height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.heroVideo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.heroVideo video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.heroOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.7), rgba(45, 45, 45, 0.5));
  z-index: 2;
}

.heroContent {
  position: relative;
  z-index: 3;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
  margin-bottom: 1rem;
}

.highlight {
  color: #d4af37;
}

.heroSubtitle {
  font-size: 1.25rem;
  color: #cccccc;
  line-height: 1.6;
}

/* Container */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Menu Section */
.menuSection {
  padding: 6rem 0;
  background: #ffffff;
}

.menuHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.menuTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.menuSubtitle {
  font-size: 1.1rem;
  color: #4a4a4a;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.categoryTabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.categoryTab {
  background: #f8f9fa;
  color: #4a4a4a;
  border: 2px solid #e9ecef;
  padding: 1rem 1.5rem;
  border-radius: 25px;
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.categoryTab:hover,
.categoryTab.active {
  background: #d4af37;
  color: #1a1a1a;
  border-color: #d4af37;
  transform: translateY(-2px);
}

.categoryIcon {
  font-size: 1.2rem;
}

.menuGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.menuItem {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  cursor: pointer;
}

.menuItem:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
  border-color: #d4af37;
}

.itemImage {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.itemImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.menuItem:hover .itemImage img {
  transform: scale(1.1);
}

.itemOverlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(212, 175, 55, 0.9);
  color: #1a1a1a;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

.itemContent {
  padding: 1.5rem;
}

.itemCategory {
  display: inline-block;
  background: #f8f9fa;
  color: #6c757d;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.itemName {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.itemDescription {
  font-size: 1rem;
  color: #4a4a4a;
  line-height: 1.6;
  margin: 0;
}

/* Custom Menu Section */
.customMenuSection {
  padding: 6rem 0;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.customMenuContent {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.customMenuTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.customMenuDescription {
  font-size: 1.25rem;
  color: #4a4a4a;
  line-height: 1.7;
  margin-bottom: 3rem;
}

.customMenuFeatures {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.featureIcon {
  font-size: 2rem;
}

.feature span:last-child {
  font-weight: 500;
  color: #1a1a1a;
}

.customMenuButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton {
  background: linear-gradient(135deg, #d4af37, #f4e4a6);
  color: #1a1a1a;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.primaryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.secondaryButton {
  background: white;
  color: #1a1a1a;
  border: 2px solid #1a1a1a;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondaryButton:hover {
  background: #1a1a1a;
  color: white;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  /* Responsive styles */
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .heroSubtitle {
    font-size: 1.1rem;
  }
  
  .categoryTabs {
    flex-direction: column;
    align-items: center;
  }
  
  .categoryTab {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
  
  .menuGrid {
    grid-template-columns: 1fr;
  }
  
  .customMenuFeatures {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .customMenuButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }
  
  .customMenuFeatures {
    grid-template-columns: 1fr;
  }
  
  .container {
    padding: 0 1rem;
  }
}
