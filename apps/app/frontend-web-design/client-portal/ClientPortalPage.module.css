.clientPortalPage {
  min-height: 100vh;
  background: white;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Section */
.heroSection {
  padding: 8rem 0 6rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  text-align: center;
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  letter-spacing: -0.02em;
  margin-bottom: 1rem;
}

.highlight {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.heroSubtitle {
  font-size: 1.25rem;
  color: #64748b;
  line-height: 1.6;
}

/* Features Section */
.featuresSection {
  padding: 6rem 0;
  background: white;
}

.sectionHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.sectionSubtitle {
  font-size: 1.25rem;
  color: #64748b;
  line-height: 1.6;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.featureCard {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 2.5rem 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.featureCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.featureCard:hover::before {
  transform: scaleX(1);
}

.featureCard:hover {
  border-color: #667eea;
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.1);
  transform: translateY(-5px);
}

.featureIcon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  display: block;
}

.featureTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.featureDescription {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

/* Benefits Section */
.benefitsSection {
  padding: 6rem 0;
  background: #f8fafc;
}

.benefitsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.benefitsTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
}

.benefitsDescription {
  font-size: 1.25rem;
  color: #64748b;
  line-height: 1.7;
  margin-bottom: 2rem;
}

.benefitsList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.benefitItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.1rem;
  color: #1e293b;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.benefitIcon {
  color: #10b981;
  font-weight: bold;
  font-size: 1.2rem;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #dcfce7;
  border-radius: 50%;
}

.benefitsVisual {
  display: flex;
  justify-content: center;
}

.portalMockup {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
  max-width: 500px;
  border: 1px solid #e2e8f0;
}

.mockupHeader {
  background: #f1f5f9;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.mockupDots {
  display: flex;
  gap: 0.5rem;
}

.mockupDots span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #cbd5e1;
}

.mockupDots span:first-child {
  background: #ef4444;
}

.mockupDots span:nth-child(2) {
  background: #f59e0b;
}

.mockupDots span:last-child {
  background: #10b981;
}

.mockupTitle {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.9rem;
}

.mockupContent {
  padding: 2rem;
  height: 300px;
  display: flex;
  gap: 1rem;
}

.mockupSidebar {
  width: 30%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.sidebarItem {
  height: 40px;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border-radius: 8px;
}

.mockupMain {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mainItem {
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 8px;
  opacity: 0.8;
}

.mainItem:nth-child(2) {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  opacity: 0.6;
  height: 80px;
}

/* Access Section */
.accessSection {
  padding: 6rem 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  text-align: center;
}

.accessContent {
  max-width: 600px;
  margin: 0 auto;
}

.accessTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.accessDescription {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  line-height: 1.6;
  opacity: 0.95;
}

.accessButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton {
  background: white;
  color: #667eea;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.primaryButton:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.secondaryButton {
  background: transparent;
  color: white;
  border: 2px solid white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.secondaryButton:hover {
  background: white;
  color: #667eea;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .benefitsGrid {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  .heroSection {
    padding: 6rem 0 4rem;
  }
  
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .featuresGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .featureCard {
    padding: 2rem 1.5rem;
  }
  
  .benefitsTitle {
    font-size: 2rem;
  }
  
  .benefitsDescription {
    font-size: 1.1rem;
  }
  
  .mockupContent {
    padding: 1.5rem;
    height: 250px;
  }
  
  .accessTitle {
    font-size: 2rem;
  }
  
  .accessButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }
  
  .featureCard {
    padding: 1.5rem;
  }
  
  .featureIcon {
    font-size: 2.5rem;
  }
  
  .mockupContent {
    flex-direction: column;
    height: auto;
    gap: 1rem;
  }
  
  .mockupSidebar {
    width: 100%;
    flex-direction: row;
  }
}
