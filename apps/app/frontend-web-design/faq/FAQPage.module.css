.faqPage {
  min-height: 100vh;
  background: white;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Section */
.heroSection {
  padding: 8rem 0 6rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  text-align: center;
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  letter-spacing: -0.02em;
  margin-bottom: 1rem;
}

.highlight {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.heroSubtitle {
  font-size: 1.25rem;
  color: #64748b;
  line-height: 1.6;
}

/* FAQ Section */
.faqSection {
  padding: 6rem 0;
  background: white;
}

.faqList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.faqItem {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.faqItem:hover {
  border-color: #667eea;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
}

.faqQuestion {
  width: 100%;
  background: none;
  border: none;
  padding: 2rem;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.faqQuestion:hover {
  background: #f8fafc;
}

.faqQuestion.active {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.questionText {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.4;
}

.faqIcon {
  font-size: 1.5rem;
  font-weight: 300;
  color: #667eea;
  flex-shrink: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f1f5f9;
  transition: all 0.3s ease;
}

.faqQuestion.active .faqIcon {
  background: #667eea;
  color: white;
}

.faqAnswer {
  overflow: hidden;
}

.answerContent {
  padding: 0 2rem 2rem;
  font-size: 1.1rem;
  color: #64748b;
  line-height: 1.7;
}

/* CTA Section */
.ctaSection {
  padding: 6rem 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  text-align: center;
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
}

.ctaTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.ctaDescription {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  line-height: 1.6;
  opacity: 0.95;
}

.ctaButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.primaryButton {
  background: white;
  color: #667eea;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.primaryButton:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.secondaryButton {
  background: transparent;
  color: white;
  border: 2px solid white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.secondaryButton:hover {
  background: white;
  color: #667eea;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  .heroSection {
    padding: 6rem 0 4rem;
  }
  
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .faqQuestion {
    padding: 1.5rem;
  }
  
  .questionText {
    font-size: 1.1rem;
  }
  
  .answerContent {
    padding: 0 1.5rem 1.5rem;
    font-size: 1rem;
  }
  
  .ctaTitle {
    font-size: 2rem;
  }
  
  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }
  
  .faqQuestion {
    padding: 1.25rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .faqIcon {
    align-self: flex-end;
    margin-top: -2rem;
  }
  
  .questionText {
    font-size: 1rem;
    padding-right: 2rem;
  }
  
  .answerContent {
    padding: 0 1.25rem 1.25rem;
    font-size: 0.95rem;
  }
}
