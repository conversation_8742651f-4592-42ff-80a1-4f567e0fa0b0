const axios = require('axios');
const cheerio = require('cheerio');
const ConsumerPulseService = require('./consumerPulseService');
const AIService = require('./aiService');

class NewsScrapingService {
  constructor() {
    this.aiService = new AIService();
    // News sources configuration
    this.newsSources = [
      {
        name: 'Reuters Business',
        url: 'https://www.reuters.com/business/',
        selectors: {
          articles: 'article',
          title: 'h3 a, h2 a',
          link: 'h3 a, h2 a',
          summary: 'p'
        }
      },
      {
        name: 'BBC Business',
        url: 'https://www.bbc.com/news/business',
        selectors: {
          articles: '[data-testid="edinburgh-article"]',
          title: 'h3',
          link: 'a',
          summary: 'p'
        }
      }
    ];

    // Keywords for traffic direction
    this.targetKeywords = [
      'market research', 'consumer behavior', 'business analytics',
      'survey data', 'polling results', 'consumer sentiment',
      'market trends', 'business intelligence', 'data analysis',
      'consumer insights', 'market analysis', 'business surveys'
    ];
  }

  // Scrape news from configured sources
  async scrapeNews() {
    const scrapedArticles = [];

    for (const source of this.newsSources) {
      try {
        console.log(`Scraping from ${source.name}...`);
        const articles = await this.scrapeFromSource(source);
        scrapedArticles.push(...articles);
      } catch (error) {
        console.error(`Error scraping ${source.name}:`, error.message);
      }
    }

    return scrapedArticles;
  }

  async scrapeFromSource(source) {
    try {
      const response = await axios.get(source.url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        timeout: 10000
      });

      const $ = cheerio.load(response.data);
      const articles = [];

      $(source.selectors.articles).each((index, element) => {
        try {
          const $article = $(element);
          const title = $article.find(source.selectors.title).first().text().trim();
          const linkElement = $article.find(source.selectors.link).first();
          const link = linkElement.attr('href');
          const summary = $article.find(source.selectors.summary).first().text().trim();

          if (title && link) {
            // Ensure absolute URL
            const absoluteLink = link.startsWith('http') ? link : new URL(link, source.url).href;
            
            articles.push({
              title,
              summary: summary || title,
              sourceUrl: absoluteLink,
              source: source.name,
              scrapedAt: new Date()
            });
          }
        } catch (error) {
          console.error('Error parsing article:', error.message);
        }
      });

      return articles.slice(0, 10); // Limit to 10 articles per source
    } catch (error) {
      throw new Error(`Failed to scrape ${source.name}: ${error.message}`);
    }
  }

  // Generate AI-powered content based on scraped news
  async generateContentFromNews(scrapedArticles) {
    const generatedArticles = [];

    for (const article of scrapedArticles) {
      try {
        // Generate enhanced content with AI
        const enhancedArticle = await this.aiService.enhanceArticleContent(article);

        // Add SEO keywords
        const keywordOptimizedArticle = this.addSEOKeywords(enhancedArticle);

        // Analyze sentiment using AI
        const sentimentAnalysis = await this.aiService.analyzeSentiment(keywordOptimizedArticle.content);
        
        const finalArticle = {
          ...keywordOptimizedArticle,
          sentiment: sentimentAnalysis.sentiment,
          sentimentScore: sentimentAnalysis.score,
          status: 'DRAFT',
          category: this.categorizeArticle(keywordOptimizedArticle.title)
        };

        generatedArticles.push(finalArticle);
      } catch (error) {
        console.error('Error generating content for article:', error.message);
      }
    }

    return generatedArticles;
  }

  // Enhance article with market research perspective
  async enhanceArticleWithMarketResearchAngle(article) {
    // This is a simplified version. In production, you'd use AI APIs like OpenAI
    const marketResearchIntro = `
      Recent market research and consumer sentiment analysis reveals important insights about ${article.title.toLowerCase()}.
      This development has significant implications for consumer behavior and market dynamics.
    `;

    const marketResearchConclusion = `
      Consumer Pulse Analysis: This trend reflects broader market sentiments that businesses should monitor closely.
      Our polling data suggests that consumer confidence in this sector may be shifting.
      For detailed market research and consumer insights, businesses can access our comprehensive analytics platform.
    `;

    const enhancedContent = `
      ${marketResearchIntro}
      
      ${article.summary}
      
      Market Research Implications:
      The developments outlined in this story highlight the importance of real-time consumer sentiment tracking.
      Businesses operating in related sectors should consider conducting targeted surveys to understand
      how these changes might affect their customer base.
      
      ${marketResearchConclusion}
    `;

    return {
      ...article,
      content: enhancedContent,
      author: 'Consumer Pulse Research Team'
    };
  }

  // Add SEO keywords strategically
  addSEOKeywords(article) {
    const relevantKeywords = this.targetKeywords.filter(keyword => 
      article.title.toLowerCase().includes(keyword.split(' ')[0]) ||
      article.content.toLowerCase().includes(keyword.split(' ')[0])
    );

    // Add 2-3 random relevant keywords if none found
    if (relevantKeywords.length === 0) {
      relevantKeywords.push(
        'market research',
        'consumer insights',
        'business analytics'
      );
    }

    // Enhance content with keywords
    const keywordSection = `
      Related Market Research Topics: ${relevantKeywords.join(', ')}
      
      For businesses seeking comprehensive ${relevantKeywords[0]} and detailed consumer behavior analysis,
      our platform provides real-time polling data and automated survey insights.
    `;

    return {
      ...article,
      content: article.content + keywordSection,
      keywords: relevantKeywords
    };
  }

  // Simple sentiment analysis (in production, use AI services)
  analyzeSentiment(content) {
    const positiveWords = ['growth', 'increase', 'positive', 'success', 'gain', 'rise', 'improve', 'strong'];
    const negativeWords = ['decline', 'decrease', 'negative', 'loss', 'fall', 'drop', 'weak', 'concern'];

    const words = content.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;

    words.forEach(word => {
      if (positiveWords.some(pw => word.includes(pw))) positiveCount++;
      if (negativeWords.some(nw => word.includes(nw))) negativeCount++;
    });

    const totalSentimentWords = positiveCount + negativeCount;
    if (totalSentimentWords === 0) {
      return { sentiment: 'NEUTRAL', score: 0 };
    }

    const score = (positiveCount - negativeCount) / totalSentimentWords;
    
    let sentiment;
    if (score > 0.2) sentiment = 'POSITIVE';
    else if (score < -0.2) sentiment = 'NEGATIVE';
    else sentiment = 'NEUTRAL';

    return { sentiment, score };
  }

  // Categorize articles
  categorizeArticle(title) {
    const categories = {
      'Technology': ['tech', 'digital', 'ai', 'software', 'data'],
      'Finance': ['bank', 'finance', 'money', 'investment', 'market'],
      'Business': ['business', 'company', 'corporate', 'industry'],
      'Economics': ['economy', 'economic', 'gdp', 'inflation', 'trade'],
      'Consumer': ['consumer', 'retail', 'shopping', 'customer']
    };

    const titleLower = title.toLowerCase();
    
    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => titleLower.includes(keyword))) {
        return category;
      }
    }

    return 'General';
  }

  // Main method to scrape and generate content
  async scrapeAndGenerateContent() {
    try {
      console.log('Starting news scraping and content generation...');
      
      // Scrape news
      const scrapedArticles = await this.scrapeNews();
      console.log(`Scraped ${scrapedArticles.length} articles`);

      if (scrapedArticles.length === 0) {
        console.log('No articles scraped');
        return { success: false, message: 'No articles found' };
      }

      // Generate enhanced content
      const generatedArticles = await this.generateContentFromNews(scrapedArticles);
      console.log(`Generated ${generatedArticles.length} enhanced articles`);

      // Save to database
      const savedArticles = [];
      for (const article of generatedArticles) {
        try {
          const result = await ConsumerPulseService.createArticle(article);
          if (result.success) {
            savedArticles.push(result.article);
          }
        } catch (error) {
          console.error('Error saving article:', error.message);
        }
      }

      console.log(`Saved ${savedArticles.length} articles to database`);

      return {
        success: true,
        scraped: scrapedArticles.length,
        generated: generatedArticles.length,
        saved: savedArticles.length,
        articles: savedArticles
      };
    } catch (error) {
      console.error('Error in scrapeAndGenerateContent:', error);
      return { success: false, error: error.message };
    }
  }

  // Schedule automatic content generation
  startAutomaticScraping(intervalHours = 6) {
    console.log(`Starting automatic news scraping every ${intervalHours} hours`);
    
    // Run immediately
    this.scrapeAndGenerateContent();
    
    // Schedule recurring runs
    setInterval(() => {
      this.scrapeAndGenerateContent();
    }, intervalHours * 60 * 60 * 1000);
  }
}

module.exports = NewsScrapingService;
